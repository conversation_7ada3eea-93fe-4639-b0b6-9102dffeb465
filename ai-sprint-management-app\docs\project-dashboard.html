<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Sprint Management App - Project Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        /* Atlassian Design System Inspired Styles */
        :root {
            /* Primary Colors */
            --color-primary: #0052CC;
            --color-secondary: #4C9AFF;
            
            /* Neutrals */
            --color-n900: #091E42;
            --color-n800: #172B4D;
            --color-n500: #6B778C;
            --color-n200: #EBECF0;
            --color-n100: #F4F5F7;
            --color-n50: #FAFBFC;
            
            /* Status Colors */
            --color-success: #36B37E;
            --color-warning: #FFAB00;
            --color-error: #DE350B;
            --color-info: #00B8D9;
            
            /* Spacing */
            --space-1: 4px;
            --space-2: 8px;
            --space-3: 12px;
            --space-4: 16px;
            --space-6: 24px;
            --space-8: 32px;
            
            /* Typography */
            --font-primary: 'Atlassian Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background-color: var(--color-n50);
            color: var(--color-n900);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-6);
        }

        .header {
            background: white;
            border-bottom: 2px solid var(--color-primary);
            padding: var(--space-6);
            margin-bottom: var(--space-8);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(9, 30, 66, 0.15);
        }

        .header h1 {
            color: var(--color-primary);
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: var(--space-2);
        }

        .header p {
            color: var(--color-n500);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .stat-card {
            background: white;
            padding: var(--space-6);
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(9, 30, 66, 0.25);
            border-left: 4px solid var(--color-primary);
        }

        .stat-card h3 {
            color: var(--color-n800);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--space-2);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: var(--space-1);
        }

        .stat-label {
            color: var(--color-n500);
            font-size: 0.875rem;
        }

        .progress-section {
            background: white;
            padding: var(--space-6);
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(9, 30, 66, 0.25);
            margin-bottom: var(--space-8);
        }

        .progress-section h2 {
            color: var(--color-n800);
            margin-bottom: var(--space-6);
            font-size: 1.5rem;
        }

        .phase-progress {
            margin-bottom: var(--space-6);
        }

        .phase-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-3);
        }

        .phase-title {
            font-weight: 600;
            color: var(--color-n800);
        }

        .phase-percentage {
            background: var(--color-n100);
            padding: var(--space-1) var(--space-2);
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-n800);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--color-n200);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
            transition: width 0.3s ease;
        }

        .chart-container {
            background: white;
            padding: var(--space-6);
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(9, 30, 66, 0.25);
            margin-bottom: var(--space-8);
        }

        .chart-container h2 {
            color: var(--color-n800);
            margin-bottom: var(--space-6);
            font-size: 1.5rem;
        }

        .mermaid {
            display: flex;
            justify-content: center;
        }

        .moscow-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .moscow-card {
            background: white;
            padding: var(--space-6);
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(9, 30, 66, 0.25);
        }

        .moscow-card.must-have { border-left: 4px solid var(--color-error); }
        .moscow-card.should-have { border-left: 4px solid var(--color-warning); }
        .moscow-card.could-have { border-left: 4px solid var(--color-info); }
        .moscow-card.wont-have { border-left: 4px solid var(--color-n500); }

        .moscow-card h3 {
            margin-bottom: var(--space-3);
            font-size: 1.1rem;
        }

        .moscow-card.must-have h3 { color: var(--color-error); }
        .moscow-card.should-have h3 { color: var(--color-warning); }
        .moscow-card.could-have h3 { color: var(--color-info); }
        .moscow-card.wont-have h3 { color: var(--color-n500); }

        .task-count {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
        }

        .footer {
            text-align: center;
            padding: var(--space-8);
            color: var(--color-n500);
            border-top: 1px solid var(--color-n200);
            margin-top: var(--space-8);
        }

        .status-badge {
            display: inline-block;
            padding: var(--space-1) var(--space-2);
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: var(--color-n200);
            color: var(--color-n800);
        }

        .status-progress {
            background: rgba(255, 171, 0, 0.2);
            color: var(--color-warning);
        }

        .status-complete {
            background: rgba(54, 179, 126, 0.2);
            color: var(--color-success);
        }

        @media (max-width: 768px) {
            .container {
                padding: var(--space-4);
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 AI Sprint Management App</h1>
            <p>Comprehensive project execution dashboard with MoSCoW prioritization and phase-wise tracking</p>
        </header>

        <!-- Project Statistics -->
        <section class="stats-grid">
            <div class="stat-card">
                <h3>Total Tasks</h3>
                <div class="stat-value">197</div>
                <div class="stat-label">Across 9 phases</div>
            </div>
            <div class="stat-card">
                <h3>Must Have</h3>
                <div class="stat-value">142</div>
                <div class="stat-label">Critical for MVP</div>
            </div>
            <div class="stat-card">
                <h3>Should Have</h3>
                <div class="stat-value">43</div>
                <div class="stat-label">Important features</div>
            </div>
            <div class="stat-card">
                <h3>Could Have</h3>
                <div class="stat-value">12</div>
                <div class="stat-label">Nice-to-have</div>
            </div>
        </section>

        <!-- Phase Progress -->
        <section class="progress-section">
            <h2>📊 Phase Progress Overview</h2>
            
            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 1: Project Foundation & Setup</span>
                    <span class="status-badge status-progress">In Progress</span>
                    <span class="phase-percentage">0% (0/24)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 2: Authentication System</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/32)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 3: User & Project Management</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/26)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 4: Board & Issue Foundation</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/28)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 5: Kanban Board Implementation</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/21)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 6: Sprint Management</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/18)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 7: AI Integration</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/18)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 8: Testing & Quality Assurance</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/18)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="phase-progress">
                <div class="phase-header">
                    <span class="phase-title">Phase 9: Deployment & Documentation</span>
                    <span class="status-badge status-pending">Pending</span>
                    <span class="phase-percentage">0% (0/12)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>
        </section>

        <!-- MoSCoW Framework Breakdown -->
        <section class="moscow-grid">
            <div class="moscow-card must-have">
                <h3>🔴 Must Have</h3>
                <div class="task-count">142 tasks</div>
                <p>Critical features required for MVP launch. These are non-negotiable and must be completed for the product to be viable.</p>
            </div>
            <div class="moscow-card should-have">
                <h3>🟡 Should Have</h3>
                <div class="task-count">43 tasks</div>
                <p>Important features that significantly enhance user satisfaction but are not critical for initial launch.</p>
            </div>
            <div class="moscow-card could-have">
                <h3>🔵 Could Have</h3>
                <div class="task-count">12 tasks</div>
                <p>Nice-to-have features that would provide additional value but can be deferred if time constraints arise.</p>
            </div>
            <div class="moscow-card wont-have">
                <h3>⚫ Won't Have</h3>
                <div class="task-count">0 tasks</div>
                <p>Features explicitly excluded from this release cycle but may be considered for future versions.</p>
            </div>
        </section>

        <!-- Project Timeline -->
        <section class="chart-container">
            <h2>📅 Project Timeline & Dependencies</h2>
            <div class="mermaid">
                gantt
                    title AI Sprint Management App Development Timeline
                    dateFormat  YYYY-MM-DD
                    section Foundation
                    Project Setup           :active, setup, 2024-01-01, 7d
                    Database Schema         :db, after setup, 3d
                    section Authentication
                    Backend Auth            :auth-be, after db, 5d
                    Frontend Auth           :auth-fe, after auth-be, 4d
                    section Core Features
                    Project Management      :proj-mgmt, after auth-fe, 7d
                    Board & Issues          :boards, after proj-mgmt, 7d
                    Kanban Implementation   :kanban, after boards, 7d
                    section Advanced
                    Sprint Management       :sprints, after kanban, 7d
                    AI Integration          :ai, after sprints, 7d
                    section Quality
                    Testing & QA            :testing, after ai, 7d
                    Deployment              :deploy, after testing, 5d
            </div>
        </section>

        <!-- Architecture Overview -->
        <section class="chart-container">
            <h2>🏗️ System Architecture Overview</h2>
            <div class="mermaid">
                graph TB
                    subgraph "Frontend (React.js)"
                        A[Landing Page] --> B[Authentication]
                        B --> C[Dashboard]
                        C --> D[Project Management]
                        D --> E[Kanban Board]
                        E --> F[AI Features]
                    end
                    
                    subgraph "Backend (Node.js + Express)"
                        G[API Gateway] --> H[Auth Middleware]
                        H --> I[User Management]
                        H --> J[Project Management]
                        H --> K[Board Management]
                        H --> L[AI Services]
                    end
                    
                    subgraph "External Services"
                        M[MySQL Database]
                        N[OpenAI API]
                        O[Email Service]
                    end
                    
                    A -.-> G
                    I --> M
                    J --> M
                    K --> M
                    L --> N
                    I --> O
                    
                    style A fill:#e1f5fe
                    style G fill:#f3e5f5
                    style M fill:#e8f5e8
                    style N fill:#fff3e0
                    style O fill:#fce4ec
            </div>
        </section>

        <footer class="footer">
            <p>Last Updated: 2024-01-01 | Next Review: After Phase 1 Completion</p>
            <p>📋 <a href="../TODO.md" style="color: var(--color-primary);">View Detailed TODO List</a> | 📖 <a href="PRD.md" style="color: var(--color-primary);">View Full PRD</a></p>
        </footer>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#0052CC',
                primaryTextColor: '#091E42',
                primaryBorderColor: '#0052CC',
                lineColor: '#6B778C',
                secondaryColor: '#F4F5F7',
                tertiaryColor: '#FAFBFC'
            }
        });

        // Add interactive progress updates (placeholder for future implementation)
        function updateProgress(phase, completed, total) {
            const percentage = Math.round((completed / total) * 100);
            const progressBar = document.querySelector(`[data-phase="${phase}"] .progress-fill`);
            const percentageText = document.querySelector(`[data-phase="${phase}"] .phase-percentage`);
            
            if (progressBar && percentageText) {
                progressBar.style.width = `${percentage}%`;
                percentageText.textContent = `${percentage}% (${completed}/${total})`;
            }
        }

        // Auto-refresh functionality (for future real-time updates)
        function refreshDashboard() {
            // This would fetch real progress data from the backend
            console.log('Dashboard refreshed at:', new Date().toLocaleTimeString());
        }

        // Refresh every 5 minutes
        setInterval(refreshDashboard, 300000);
    </script>
</body>
</html>
