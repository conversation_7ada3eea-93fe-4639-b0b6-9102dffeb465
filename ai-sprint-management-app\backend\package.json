{"name": "ai-sprint-management-backend", "version": "1.0.0", "description": "AI-powered Sprint Management App Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "keywords": ["sprint-management", "kanban", "project-management", "ai", "nodejs", "express"], "author": "AI Sprint Management Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "nodemailer": "^6.9.7", "dotenv": "^16.3.1", "winston": "^3.11.0", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.1", "openai": "^4.20.1", "multer": "^1.4.5-lts.1", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}